<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<projectDescription>
	<name>adv_ali_light</name>
	<comment/>
	<projects>
	</projects>
	<buildSpec>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
			<triggers>clean,full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
		<buildCommand>
			<name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
			<triggers>full,incremental,</triggers>
			<arguments>
			</arguments>
		</buildCommand>
	</buildSpec>
	<natures>
		<nature>org.eclipse.cdt.core.cnature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
		<nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
	</natures>
	<linkedResources>
		<link>
			<name>HAL</name>
			<type>2</type>
			<locationURI>PARENT-2-PROJECT_LOC/HAL</locationURI>
		</link>
		<link>
			<name>LIB</name>
			<type>2</type>
			<locationURI>PARENT-2-PROJECT_LOC/LIB</locationURI>
		</link>
		<link>
			<name>Ld</name>
			<type>2</type>
			<locationURI>PARENT-3-PROJECT_LOC/SRC/Ld</locationURI>
		</link>
		<link>
			<name>MESH_LIB</name>
			<type>2</type>
			<locationURI>PARENT-1-PROJECT_LOC/MESH_LIB</locationURI>
		</link>
		<link>
			<name>RVMSIS</name>
			<type>2</type>
			<locationURI>PARENT-3-PROJECT_LOC/SRC/RVMSIS</locationURI>
		</link>
		<link>
			<name>Startup</name>
			<type>2</type>
			<locationURI>PARENT-3-PROJECT_LOC/SRC/Startup</locationURI>
		</link>
		<link>
			<name>StdPeriphDriver</name>
			<type>2</type>
			<locationURI>PARENT-3-PROJECT_LOC/SRC/StdPeriphDriver</locationURI>
		</link>
	</linkedResources>
	<filteredResources>
		<filter>
			<id>1616659439929</id>
			<name/>
			<type>22</type>
			<matcher>
				<id>org.eclipse.ui.ide.multiFilter</id>
				<arguments>1.0-name-matches-false-false-*.wvproj</arguments>
			</matcher>
		</filter>
	</filteredResources>
</projectDescription>
