ch585 BLE+IoChub简单灯控开关应用:

例程说明:本例程实现远程手机通过Iochub网关连接开发板控制灯亮灭和物理按键开关状态切换上报效果。

硬件IO：蓝牙连接状态指示灯-PB19	LED灯控引脚-PB18		切换开关-PB4

测试流程：
1, 将网关设备接入网络，并打开WCHIoCHubGateWay软件启动服务，查看当前会话信息获取到本机ID和会话秘钥。
2, 主机端打开IoCHubNetApp软件，同样启动服务，并通过填入步骤1中获取到的ID和秘钥打开会话。
3, 将开发板上电，在网关设备的软件下进行枚举设备操作，并连接设备名为ch585_ble_iochub名称的设备。
4, 显示连接成功后，选中会话列表中对应的通信会话，则可以在主机端软件发现设备列表Mac，灯光状态和开关状态。
5, 此时可以通过软件端控制开发板上灯的亮灭，并可以通过按下开发板的按键开发来切换开关状态显示在APP上。

APP下载路径：
https://www.wch.cn/downloads/BleToNet_Android_ZIP.html 
