<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<projectDescription>
  <name>HID_BTSSP_NFC</name>
  <comment/>
  <projects/>
  <buildSpec>
    <buildCommand>
      <name>org.eclipse.cdt.managedbuilder.core.genmakebuilder</name>
      <triggers>clean,full,incremental,</triggers>
      <arguments/>
    </buildCommand>
    <buildCommand>
      <name>org.eclipse.cdt.managedbuilder.core.ScannerConfigBuilder</name>
      <triggers>full,incremental,</triggers>
      <arguments/>
    </buildCommand>
  </buildSpec>
  <natures>
    <nature>org.eclipse.cdt.core.cnature</nature>
    <nature>org.eclipse.cdt.managedbuilder.core.managedBuildNature</nature>
    <nature>org.eclipse.cdt.managedbuilder.core.ScannerConfigNature</nature>
  </natures>
  <linkedResources>
    <link>
      <name>HAL</name>
      <type>2</type>
      <locationURI>PARENT-1-PROJECT_LOC/HAL</locationURI>
    </link>
    <link>
      <name>LIB</name>
      <type>2</type>
      <locationURI>PARENT-1-PROJECT_LOC/LIB</locationURI>
    </link>
    <link>
      <name>Ld</name>
      <type>2</type>
      <locationURI>PARENT-2-PROJECT_LOC/SRC/Ld</locationURI>
    </link>
    <link>
      <name>RVMSIS</name>
      <type>2</type>
      <locationURI>PARENT-2-PROJECT_LOC/SRC/RVMSIS</locationURI>
    </link>
    <link>
      <name>Startup</name>
      <type>2</type>
      <locationURI>PARENT-2-PROJECT_LOC/SRC/Startup</locationURI>
    </link>
    <link>
      <name>StdPeriphDriver</name>
      <type>2</type>
      <locationURI>PARENT-2-PROJECT_LOC/SRC/StdPeriphDriver</locationURI>
    </link>
    <link>
      <name>T2T_COMMON</name>
      <type>2</type>
      <locationURI>PARENT-2-PROJECT_LOC/NFCA/PICC/PICC_T2T/T2T_COMMON</locationURI>
    </link>
    <link>
      <name>NFCA_LIB</name>
      <type>2</type>
      <locationURI>PARENT-2-PROJECT_LOC/NFCA/NFCA_LIB</locationURI>
    </link>
    <link>
      <name>PICC_COMMON</name>
      <type>2</type>
      <locationURI>PARENT-2-PROJECT_LOC/NFCA/PICC/PICC_COMMON</locationURI>
    </link>
  </linkedResources>
  <filteredResources>
    <filter>
      <name/>
      <type>6</type>
      <matcher>
        <id>org.eclipse.ui.ide.multiFilter</id>
        <arguments>1.0-name-matches-false-false-*.wvproj</arguments>
      </matcher>
    </filter>
  </filteredResources>
</projectDescription>